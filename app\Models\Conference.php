<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Conference extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'title',
        'subtitle',
        'description',
        'start_date',
        'end_date',
        'location',
        'venue',
        'logo',
        'banner_image',
        'video_url',
        'registration_fee_local',
        'registration_fee_international',
        'currency_local',
        'currency_international',
        'abstract_deadline',
        'notification_date',
        'final_paper_deadline',
        'email',
        'phone',
        'website',
        'status',
        'meta_title',
        'meta_description',
        'meta_keywords'
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'abstract_deadline' => 'datetime',
        'notification_date' => 'datetime',
        'final_paper_deadline' => 'datetime',
        'registration_fee_local' => 'decimal:2',
        'registration_fee_international' => 'decimal:2'
    ];

    protected $dates = [
        'start_date',
        'end_date',
        'abstract_deadline',
        'notification_date',
        'final_paper_deadline'
    ];

    // Relationships
    public function speakers()
    {
        return $this->hasMany(Speaker::class);
    }

    public function events()
    {
        return $this->hasMany(Event::class);
    }

    public function registrations()
    {
        return $this->hasMany(Registration::class);
    }

    public function partners()
    {
        return $this->hasMany(Partner::class);
    }

    public function news()
    {
        return $this->hasMany(News::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeUpcoming($query)
    {
        return $query->where('start_date', '>', now());
    }

    // Accessors
    public function getFormattedStartDateAttribute()
    {
        return $this->start_date ? $this->start_date->format('F j, Y') : null;
    }

    public function getFormattedEndDateAttribute()
    {
        return $this->end_date ? $this->end_date->format('F j, Y') : null;
    }

    public function getDurationAttribute()
    {
        if ($this->start_date && $this->end_date) {
            return $this->start_date->format('F j') . '-' . $this->end_date->format('j, Y');
        }
        return null;
    }

    public function getLogoUrlAttribute()
    {
        return $this->logo ? asset('storage/' . $this->logo) : asset('images/default-logo.png');
    }

    public function getBannerImageUrlAttribute()
    {
        return $this->banner_image ? asset('storage/' . $this->banner_image) : asset('images/default-banner.jpg');
    }
}
